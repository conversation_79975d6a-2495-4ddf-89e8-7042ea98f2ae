<?php

namespace App\Filament\Resources\Admin\DavharResource\Pages;

use App\Filament\Resources\Admin\DavharResource;
use App\Models\Davhar;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Log;

class CreateDavhar extends CreateRecord
{
    protected static string $resource = DavharResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // If orc_id is passed via URL parameter, set it
        if (request()->has('orc_id')) {
            $data[Davhar::ORC_ID] = request()->get('orc_id');
        }

        return parent::mutateFormDataBeforeCreate($data);
    }

    public function getBreadcrumbs(): array
    {
        $orcId = request()->get('orc_id');
        $breadcrumbs = [
            url()->route('filament.admin.resources.bairs.index') => 'Байрууд',
        ];

        if ($orcId) {
            $orc = \App\Models\Orc::find($orcId);
            if ($orc) {
                $breadcrumbs[url()->route('filament.admin.resources.bairs.edit', $orc->korpus->bair_id)] = $orc->korpus->bair->name;
                $breadcrumbs[url()->route('filament.admin.resources.korpuses.edit', $orc->korpus_id)] = "Блок: {$orc->korpus->name}";
                $breadcrumbs[url()->route('filament.admin.resources.orcs.edit', $orcId)] = "Орц: {$orc->number}";
            }
        }

        $breadcrumbs['#'] = 'Давхар нэмэх';

        return $breadcrumbs;
    }

    protected function getRedirectUrl(): string
    {
        $orcId = request()->get('orc_id');
        Log::info('CreateDavhar getRedirectUrl called', ['orc_id' => $orcId]);

        if ($orcId) {
            $url = \App\Filament\Resources\Admin\OrcResource::getUrl('edit', ['record' => $orcId]);
            Log::info('Redirecting to Orc edit page', ['url' => $url]);
            return $url;
        }

        $url = $this->getResource()::getUrl('edit', ['record' => $this->getRecord()]);
        Log::info('Redirecting to Davhar edit page', ['url' => $url]);
        return $url;
    }
}
