<?php

namespace App\Filament\Resources\Admin\OrcResource\Pages;

use App\Filament\Resources\Admin\OrcResource;
use App\Models\Orc;
use App\Models\Davhar;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Log;

class CreateOrc extends CreateRecord
{
    protected static string $resource = OrcResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // If korpus_id is passed via URL parameter, set it
        if (request()->has('korpus_id')) {
            $data[Orc::KORPUS_ID] = request()->get('korpus_id');
        }

        return parent::mutateFormDataBeforeCreate($data);
    }

    protected function afterCreate(): void
    {
        $record = $this->record;
        // Create default Davhar when Orc is created
        $davhar = new Davhar([
            Davhar::ORC_ID => $record->id,
            Davhar::NUMBER => 1
        ]);
        $record->davhars()->save($davhar);
    }

    public function getBreadcrumbs(): array
    {
        $korpusId = request()->get('korpus_id');
        $breadcrumbs = [
            url()->route('filament.admin.resources.bairs.index') => 'Байрууд',
        ];

        if ($korpusId) {
            $korpus = \App\Models\Korpus::find($korpusId);
            if ($korpus) {
                $breadcrumbs[url()->route('filament.admin.resources.bairs.edit', $korpus->bair_id)] = $korpus->bair->name;
                $breadcrumbs[url()->route('filament.admin.resources.korpuses.edit', $korpusId)] = "Блок: {$korpus->name}";
            }
        }

        $breadcrumbs['#'] = 'Орц нэмэх';

        return $breadcrumbs;
    }

    protected function getRedirectUrl(): string
    {
        $korpusId = request()->get('korpus_id');
        Log::info('CreateOrc getRedirectUrl called', ['korpus_id' => $korpusId]);

        if ($korpusId) {
            $url = \App\Filament\Resources\Admin\KorpusResource::getUrl('edit', ['record' => $korpusId]);
            Log::info('Redirecting to Korpus edit page', ['url' => $url]);
            return $url;
        }

        $url = $this->getResource()::getUrl('edit', ['record' => $this->getRecord()]);
        Log::info('Redirecting to Orc edit page', ['url' => $url]);
        return $url;
    }
}
